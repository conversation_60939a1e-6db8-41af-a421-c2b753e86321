# 临时邮箱HTML格式显示任务

## 任务背景
用户希望将临时邮箱的邮件内容从text文档格式改为HTML页面格式，主要目的是让邮件中的URL链接可以直接点击。

## 执行计划

### 1. 修改邮件内容获取逻辑
- **文件**: `utility_tools.user.js`
- **位置**: 第1075行
- **操作**: 将 `detail.body?.text || detail.body?.html` 改为 `detail.body?.html || detail.body?.text`
- **目的**: 优先使用HTML格式内容

### 2. 修改内容显示方式
- **文件**: `utility_tools.user.js` 
- **位置**: 第1088行
- **操作**: 
  - 移除 `white-space: pre-wrap` 样式（这会阻止HTML渲染）
  - 使用 `innerHTML` 而不是文本插值来渲染内容
  - 添加基本的安全过滤函数

### 3. 添加HTML安全过滤函数
- **文件**: `utility_tools.user.js`
- **位置**: 在邮件功能区域添加新函数
- **功能**: 
  - 移除危险的script、iframe等标签
  - 保留链接、图片、基本格式标签
  - 确保链接在新窗口打开

### 4. 优化HTML邮件样式
- **文件**: `utility_tools.user.js`
- **位置**: 邮件内容容器样式
- **操作**:
  - 添加CSS样式隔离
  - 确保邮件内容不会影响插件界面
  - 优化链接显示效果

## 预期结果
- 邮件中的URL链接可以直接点击
- HTML格式的邮件能正常显示格式
- 保持插件界面的稳定性和安全性
- 如果邮件只有文本格式，会自动转换URL为可点击链接

## 执行状态
- [x] 计划制定完成
- [x] 步骤1：修改邮件内容获取逻辑 - 已完成，优先使用HTML格式
- [x] 步骤2：修改内容显示方式 - 已完成，移除white-space: pre-wrap，使用innerHTML渲染
- [x] 步骤3：添加HTML安全过滤函数 - 已完成，包含URL转换和安全过滤
- [x] 步骤4：优化HTML邮件样式 - 已完成，添加样式隔离和链接优化
- [ ] 测试验证

## 实际修改内容
1. 在第1006行添加了`sanitizeHtml`函数，支持：
   - 纯文本URL自动转换为可点击链接
   - HTML内容安全过滤，移除危险标签
   - 链接强制在新窗口打开
   - 图片自适应大小

2. 在第1119-1121行修改邮件内容获取逻辑：
   - 优先使用`detail.body?.html`
   - 通过`sanitizeHtml`函数处理内容

3. 在第1127-1152行重构邮件内容显示：
   - 移除`white-space: pre-wrap`样式
   - 使用`innerHTML`渲染HTML内容
   - 添加`word-wrap: break-word`确保长文本正确换行
